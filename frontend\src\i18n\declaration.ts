// this file generate by script, don't modify it manually!!!
export enum I18nKey {
  STATUS$WEBSOCKET_CLOSED = "STATUS$WEBSOCKET_CLOSED",
  HOME$LAUNCH_FROM_SCRATCH = "HOME$LAUNCH_FROM_SCRATCH",
  HOME$READ_THIS = "HOME$READ_THIS",
  AUTH$LOGGING_BACK_IN = "AUTH$LOGGING_BACK_IN",
  SECURITY$LOW_RISK = "SECURITY$LOW_RISK",
  SECURITY$MEDIUM_RISK = "SECURITY$MEDIUM_RISK",
  SECURITY$HIGH_RISK = "SECURITY$HIGH_RISK",
  SECURITY$UNKNOWN_RISK = "SECURITY$UNKNOWN_RISK",
  FINISH$TASK_COMPLETED_SUCCESSFULLY = "FINISH$TASK_COMPLETED_SUCCESSFULLY",
  FINISH$TASK_NOT_COMPLETED = "FINISH$TASK_NOT_COMPLETED",
  FINISH$TASK_COMPLETED_PARTIALLY = "FINISH$TASK_COMPLETED_PARTIALLY",
  EVENT$UNKNOWN_EVENT = "EVENT$UNKNOWN_EVENT",
  OBSERVATION$COMMAND_NO_OUTPUT = "OBSERVATION$COMMAND_NO_OUTPUT",
  OBSERVATION$MCP_NO_OUTPUT = "OBSERVATION$MCP_NO_OUTPUT",
  MCP_OBSERVATION$ARGUMENTS = "MCP_OBSERVATION$ARGUMENTS",
  MCP_OBSERVATION$OUTPUT = "MCP_OBSERVATION$OUTPUT",
  OBSERVATION$ERROR_PREFIX = "OBSERVATION$ERROR_PREFIX",
  TASK$ADDRESSING_TASK = "TASK$ADDRESSING_TASK",
  SECRETS$SECRET_VALUE_REQUIRED = "SECRETS$SECRET_VALUE_REQUIRED",
  SECRETS$ADD_SECRET = "SECRETS$ADD_SECRET",
  SECRETS$EDIT_SECRET = "SECRETS$EDIT_SECRET",
  SECRETS$NO_SECRETS_FOUND = "SECRETS$NO_SECRETS_FOUND",
  SECRETS$ADD_NEW_SECRET = "SECRETS$ADD_NEW_SECRET",
  SECRETS$CONFIRM_DELETE_KEY = "SECRETS$CONFIRM_DELETE_KEY",
  SETTINGS$MCP_TITLE = "SETTINGS$MCP_TITLE",
  SETTINGS$MCP_DESCRIPTION = "SETTINGS$MCP_DESCRIPTION",
  SETTINGS$NAV_MCP = "SETTINGS$NAV_MCP",
  SETTINGS$MCP_CONFIGURATION = "SETTINGS$MCP_CONFIGURATION",
  SETTINGS$MCP_EDIT_CONFIGURATION = "SETTINGS$MCP_EDIT_CONFIGURATION",
  SETTINGS$MCP_CANCEL = "SETTINGS$MCP_CANCEL",
  SETTINGS$MCP_APPLY_CHANGES = "SETTINGS$MCP_APPLY_CHANGES",
  SETTINGS$MCP_CONFIG_DESCRIPTION = "SETTINGS$MCP_CONFIG_DESCRIPTION",
  SETTINGS$MCP_CONFIG_ERROR = "SETTINGS$MCP_CONFIG_ERROR",
  SETTINGS$MCP_CONFIG_EXAMPLE = "SETTINGS$MCP_CONFIG_EXAMPLE",
  SETTINGS$MCP_NO_SERVERS_CONFIGURED = "SETTINGS$MCP_NO_SERVERS_CONFIGURED",
  SETTINGS$MCP_SSE_SERVERS = "SETTINGS$MCP_SSE_SERVERS",
  SETTINGS$MCP_STDIO_SERVERS = "SETTINGS$MCP_STDIO_SERVERS",
  SETTINGS$MCP_API_KEY = "SETTINGS$MCP_API_KEY",
  SETTINGS$MCP_API_KEY_NOT_SET = "SETTINGS$MCP_API_KEY_NOT_SET",
  SETTINGS$MCP_COMMAND = "SETTINGS$MCP_COMMAND",
  SETTINGS$MCP_ARGS = "SETTINGS$MCP_ARGS",
  SETTINGS$MCP_ENV = "SETTINGS$MCP_ENV",
  SETTINGS$MCP_NAME = "SETTINGS$MCP_NAME",
  SETTINGS$MCP_URL = "SETTINGS$MCP_URL",
  SETTINGS$MCP_LEARN_MORE = "SETTINGS$MCP_LEARN_MORE",
  SETTINGS$MCP_ERROR_SSE_ARRAY = "SETTINGS$MCP_ERROR_SSE_ARRAY",
  SETTINGS$MCP_ERROR_STDIO_ARRAY = "SETTINGS$MCP_ERROR_STDIO_ARRAY",
  SETTINGS$MCP_ERROR_SSE_URL = "SETTINGS$MCP_ERROR_SSE_URL",
  SETTINGS$MCP_ERROR_STDIO_PROPS = "SETTINGS$MCP_ERROR_STDIO_PROPS",
  SETTINGS$MCP_ERROR_INVALID_JSON = "SETTINGS$MCP_ERROR_INVALID_JSON",
  SETTINGS$MCP_DEFAULT_CONFIG = "SETTINGS$MCP_DEFAULT_CONFIG",
  HOME$CONNECT_PROVIDER_MESSAGE = "HOME$CONNECT_PROVIDER_MESSAGE",
  HOME$LETS_START_BUILDING = "HOME$LETS_START_BUILDING",
  HOME$OPENHANDS_DESCRIPTION = "HOME$OPENHANDS_DESCRIPTION",
  HOME$NOT_SURE_HOW_TO_START = "HOME$NOT_SURE_HOW_TO_START",
  HOME$CONNECT_TO_REPOSITORY = "HOME$CONNECT_TO_REPOSITORY",
  HOME$LOADING = "HOME$LOADING",
  HOME$LOADING_REPOSITORIES = "HOME$LOADING_REPOSITORIES",
  HOME$FAILED_TO_LOAD_REPOSITORIES = "HOME$FAILED_TO_LOAD_REPOSITORIES",
  HOME$LOADING_BRANCHES = "HOME$LOADING_BRANCHES",
  HOME$FAILED_TO_LOAD_BRANCHES = "HOME$FAILED_TO_LOAD_BRANCHES",
  HOME$OPEN_ISSUE = "HOME$OPEN_ISSUE",
  HOME$FIX_FAILING_CHECKS = "HOME$FIX_FAILING_CHECKS",
  HOME$RESOLVE_MERGE_CONFLICTS = "HOME$RESOLVE_MERGE_CONFLICTS",
  HOME$RESOLVE_UNRESOLVED_COMMENTS = "HOME$RESOLVE_UNRESOLVED_COMMENTS",
  HOME$LAUNCH = "HOME$LAUNCH",
  SETTINGS$ADVANCED = "SETTINGS$ADVANCED",
  SETTINGS$BASE_URL = "SETTINGS$BASE_URL",
  SETTINGS$AGENT = "SETTINGS$AGENT",
  SETTINGS$ENABLE_MEMORY_CONDENSATION = "SETTINGS$ENABLE_MEMORY_CONDENSATION",
  SETTINGS$LANGUAGE = "SETTINGS$LANGUAGE",
  ACTION$PUSH_TO_BRANCH = "ACTION$PUSH_TO_BRANCH",
  ACTION$PUSH_CREATE_PR = "ACTION$PUSH_CREATE_PR",
  ACTION$PUSH_CHANGES_TO_PR = "ACTION$PUSH_CHANGES_TO_PR",
  ANALYTICS$TITLE = "ANALYTICS$TITLE",
  ANALYTICS$DESCRIPTION = "ANALYTICS$DESCRIPTION",
  ANALYTICS$SEND_ANONYMOUS_DATA = "ANALYTICS$SEND_ANONYMOUS_DATA",
  ANALYTICS$CONFIRM_PREFERENCES = "ANALYTICS$CONFIRM_PREFERENCES",
  SETTINGS$SAVING = "SETTINGS$SAVING",
  SETTINGS$SAVE_CHANGES = "SETTINGS$SAVE_CHANGES",
  SETTINGS$NAV_INTEGRATIONS = "SETTINGS$NAV_INTEGRATIONS",
  SETTINGS$NAV_APPLICATION = "SETTINGS$NAV_APPLICATION",
  SETTINGS$NAV_CREDITS = "SETTINGS$NAV_CREDITS",
  SETTINGS$NAV_SECRETS = "SETTINGS$NAV_SECRETS",
  SETTINGS$NAV_API_KEYS = "SETTINGS$NAV_API_KEYS",
  SETTINGS$NAV_LLM = "SETTINGS$NAV_LLM",
  GIT$MERGE_REQUEST = "GIT$MERGE_REQUEST",
  GIT$GITLAB_API = "GIT$GITLAB_API",
  GIT$PULL_REQUEST = "GIT$PULL_REQUEST",
  GIT$GITHUB_API = "GIT$GITHUB_API",
  BUTTON$COPY = "BUTTON$COPY",
  BUTTON$COPIED = "BUTTON$COPIED",
  APP$TITLE = "APP$TITLE",
  BROWSER$TITLE = "BROWSER$TITLE",
  BROWSER$EMPTY_MESSAGE = "BROWSER$EMPTY_MESSAGE",
  SETTINGS$TITLE = "SETTINGS$TITLE",
  CONVERSATION$START_NEW = "CONVERSATION$START_NEW",
  ACCOUNT_SETTINGS$TITLE = "ACCOUNT_SETTINGS$TITLE",
  WORKSPACE$TERMINAL_TAB_LABEL = "WORKSPACE$TERMINAL_TAB_LABEL",
  WORKSPACE$BROWSER_TAB_LABEL = "WORKSPACE$BROWSER_TAB_LABEL",
  WORKSPACE$JUPYTER_TAB_LABEL = "WORKSPACE$JUPYTER_TAB_LABEL",
  WORKSPACE$CODE_EDITOR_TAB_LABEL = "WORKSPACE$CODE_EDITOR_TAB_LABEL",
  WORKSPACE$TITLE = "WORKSPACE$TITLE",
  TERMINAL$WAITING_FOR_CLIENT = "TERMINAL$WAITING_FOR_CLIENT",
  CODE_EDITOR$FILE_SAVED_SUCCESSFULLY = "CODE_EDITOR$FILE_SAVED_SUCCESSFULLY",
  CODE_EDITOR$SAVING_LABEL = "CODE_EDITOR$SAVING_LABEL",
  CODE_EDITOR$SAVE_LABEL = "CODE_EDITOR$SAVE_LABEL",
  CODE_EDITOR$OPTIONS = "CODE_EDITOR$OPTIONS",
  CODE_EDITOR$FILE_SAVE_ERROR = "CODE_EDITOR$FILE_SAVE_ERROR",
  CODE_EDITOR$EMPTY_MESSAGE = "CODE_EDITOR$EMPTY_MESSAGE",
  FILE_SERVICE$SELECT_FILE_ERROR = "FILE_SERVICE$SELECT_FILE_ERROR",
  FILE_SERVICE$UPLOAD_FILES_ERROR = "FILE_SERVICE$UPLOAD_FILES_ERROR",
  FILE_SERVICE$LIST_FILES_ERROR = "FILE_SERVICE$LIST_FILES_ERROR",
  FILE_SERVICE$SAVE_FILE_ERROR = "FILE_SERVICE$SAVE_FILE_ERROR",
  SUGGESTIONS$INCREASE_TEST_COVERAGE = "SUGGESTIONS$INCREASE_TEST_COVERAGE",
  SUGGESTIONS$AUTO_MERGE_PRS = "SUGGESTIONS$AUTO_MERGE_PRS",
  SUGGESTIONS$FIX_README = "SUGGESTIONS$FIX_README",
  SUGGESTIONS$CLEAN_DEPENDENCIES = "SUGGESTIONS$CLEAN_DEPENDENCIES",
  SETTINGS$LLM_SETTINGS = "SETTINGS$LLM_SETTINGS",
  SETTINGS$GIT_SETTINGS = "SETTINGS$GIT_SETTINGS",
  SETTINGS$SOUND_NOTIFICATIONS = "SETTINGS$SOUND_NOTIFICATIONS",
  SETTINGS$PROACTIVE_CONVERSATION_STARTERS = "SETTINGS$PROACTIVE_CONVERSATION_STARTERS",
  SETTINGS$SEARCH_API_KEY = "SETTINGS$SEARCH_API_KEY",
  SETTINGS$SEARCH_API_KEY_OPTIONAL = "SETTINGS$SEARCH_API_KEY_OPTIONAL",
  SETTINGS$SEARCH_API_KEY_INSTRUCTIONS = "SETTINGS$SEARCH_API_KEY_INSTRUCTIONS",
  SETTINGS$CUSTOM_MODEL = "SETTINGS$CUSTOM_MODEL",
  GITHUB$CODE_NOT_IN_GITHUB = "GITHUB$CODE_NOT_IN_GITHUB",
  GITHUB$START_FROM_SCRATCH = "GITHUB$START_FROM_SCRATCH",
  AVATAR$ALT_TEXT = "AVATAR$ALT_TEXT",
  BRANDING$ALL_HANDS_AI = "BRANDING$ALL_HANDS_AI",
  BRANDING$ALL_HANDS_LOGO = "BRANDING$ALL_HANDS_LOGO",
  ERROR$GENERIC = "ERROR$GENERIC",
  GITHUB$AUTH_SCOPE = "GITHUB$AUTH_SCOPE",
  FILE_SERVICE$INVALID_FILE_PATH = "FILE_SERVICE$INVALID_FILE_PATH",
  VSCODE$OPEN = "VSCODE$OPEN",
  VSCODE$TITLE = "VSCODE$TITLE",
  VSCODE$LOADING = "VSCODE$LOADING",
  VSCODE$URL_NOT_AVAILABLE = "VSCODE$URL_NOT_AVAILABLE",
  VSCODE$FETCH_ERROR = "VSCODE$FETCH_ERROR",
  VSCODE$CROSS_ORIGIN_WARNING = "VSCODE$CROSS_ORIGIN_WARNING",
  VSCODE$URL_PARSE_ERROR = "VSCODE$URL_PARSE_ERROR",
  VSCODE$OPEN_IN_NEW_TAB = "VSCODE$OPEN_IN_NEW_TAB",
  INCREASE_TEST_COVERAGE = "INCREASE_TEST_COVERAGE",
  AUTO_MERGE_PRS = "AUTO_MERGE_PRS",
  FIX_README = "FIX_README",
  CLEAN_DEPENDENCIES = "CLEAN_DEPENDENCIES",
  CONFIGURATION$OPENHANDS_WORKSPACE_DIRECTORY_INPUT_LABEL = "CONFIGURATION$OPENHANDS_WORKSPACE_DIRECTORY_INPUT_LABEL",
  LLM$PROVIDER = "LLM$PROVIDER",
  LLM$SELECT_PROVIDER_PLACEHOLDER = "LLM$SELECT_PROVIDER_PLACEHOLDER",
  API$KEY = "API$KEY",
  API$DONT_KNOW_KEY = "API$DONT_KNOW_KEY",
  BUTTON$SAVE = "BUTTON$SAVE",
  BUTTON$CLOSE = "BUTTON$CLOSE",
  MODAL$CONFIRM_RESET_TITLE = "MODAL$CONFIRM_RESET_TITLE",
  MODAL$CONFIRM_RESET_MESSAGE = "MODAL$CONFIRM_RESET_MESSAGE",
  MODAL$END_SESSION_TITLE = "MODAL$END_SESSION_TITLE",
  MODAL$END_SESSION_MESSAGE = "MODAL$END_SESSION_MESSAGE",
  BUTTON$END_SESSION = "BUTTON$END_SESSION",
  BUTTON$CANCEL = "BUTTON$CANCEL",
  EXIT_PROJECT$CONFIRM = "EXIT_PROJECT$CONFIRM",
  EXIT_PROJECT$TITLE = "EXIT_PROJECT$TITLE",
  LANGUAGE$LABEL = "LANGUAGE$LABEL",
  GITHUB$TOKEN_LABEL = "GITHUB$TOKEN_LABEL",
  GITHUB$HOST_LABEL = "GITHUB$HOST_LABEL",
  GITHUB$TOKEN_OPTIONAL = "GITHUB$TOKEN_OPTIONAL",
  GITHUB$GET_TOKEN = "GITHUB$GET_TOKEN",
  GITHUB$TOKEN_HELP_TEXT = "GITHUB$TOKEN_HELP_TEXT",
  GITHUB$TOKEN_LINK_TEXT = "GITHUB$TOKEN_LINK_TEXT",
  GITHUB$INSTRUCTIONS_LINK_TEXT = "GITHUB$INSTRUCTIONS_LINK_TEXT",
  COMMON$HERE = "COMMON$HERE",
  ANALYTICS$ENABLE = "ANALYTICS$ENABLE",
  GITHUB$TOKEN_INVALID = "GITHUB$TOKEN_INVALID",
  BUTTON$DISCONNECT = "BUTTON$DISCONNECT",
  GITHUB$CONFIGURE_REPOS = "GITHUB$CONFIGURE_REPOS",
  SLACK$INSTALL_APP = "SLACK$INSTALL_APP",
  COMMON$CLICK_FOR_INSTRUCTIONS = "COMMON$CLICK_FOR_INSTRUCTIONS",
  LLM$SELECT_MODEL_PLACEHOLDER = "LLM$SELECT_MODEL_PLACEHOLDER",
  LLM$MODEL = "LLM$MODEL",
  CONFIGURATION$OPENHANDS_WORKSPACE_DIRECTORY_INPUT_PLACEHOLDER = "CONFIGURATION$OPENHANDS_WORKSPACE_DIRECTORY_INPUT_PLACEHOLDER",
  CONFIGURATION$MODAL_TITLE = "CONFIGURATION$MODAL_TITLE",
  CONFIGURATION$MODEL_SELECT_LABEL = "CONFIGURATION$MODEL_SELECT_LABEL",
  CONFIGURATION$MODEL_SELECT_PLACEHOLDER = "CONFIGURATION$MODEL_SELECT_PLACEHOLDER",
  CONFIGURATION$AGENT_SELECT_LABEL = "CONFIGURATION$AGENT_SELECT_LABEL",
  CONFIGURATION$AGENT_SELECT_PLACEHOLDER = "CONFIGURATION$AGENT_SELECT_PLACEHOLDER",
  CONFIGURATION$LANGUAGE_SELECT_LABEL = "CONFIGURATION$LANGUAGE_SELECT_LABEL",
  CONFIGURATION$LANGUAGE_SELECT_PLACEHOLDER = "CONFIGURATION$LANGUAGE_SELECT_PLACEHOLDER",
  CONFIGURATION$SECURITY_SELECT_LABEL = "CONFIGURATION$SECURITY_SELECT_LABEL",
  CONFIGURATION$SECURITY_SELECT_PLACEHOLDER = "CONFIGURATION$SECURITY_SELECT_PLACEHOLDER",
  CONFIGURATION$MODAL_CLOSE_BUTTON_LABEL = "CONFIGURATION$MODAL_CLOSE_BUTTON_LABEL",
  CONFIGURATION$MODAL_SAVE_BUTTON_LABEL = "CONFIGURATION$MODAL_SAVE_BUTTON_LABEL",
  CONFIGURATION$MODAL_RESET_BUTTON_LABEL = "CONFIGURATION$MODAL_RESET_BUTTON_LABEL",
  STATUS$CONNECTED_TO_SERVER = "STATUS$CONNECTED_TO_SERVER",
  PROJECT$NEW_PROJECT = "PROJECT$NEW_PROJECT",
  BROWSER$SCREENSHOT = "BROWSER$SCREENSHOT",
  TIME$MINUTES_AGO = "TIME$MINUTES_AGO",
  TIME$HOURS_AGO = "TIME$HOURS_AGO",
  TIME$DAYS_AGO = "TIME$DAYS_AGO",
  SETTINGS_FORM$RUNTIME_SIZE_LABEL = "SETTINGS_FORM$RUNTIME_SIZE_LABEL",
  CONFIGURATION$SETTINGS_NEED_UPDATE_MESSAGE = "CONFIGURATION$SETTINGS_NEED_UPDATE_MESSAGE",
  CONFIGURATION$AGENT_LOADING = "CONFIGURATION$AGENT_LOADING",
  CONFIGURATION$AGENT_RUNNING = "CONFIGURATION$AGENT_RUNNING",
  CONFIGURATION$ERROR_FETCH_MODELS = "CONFIGURATION$ERROR_FETCH_MODELS",
  CONFIGURATION$SETTINGS_NOT_FOUND = "CONFIGURATION$SETTINGS_NOT_FOUND",
  CONNECT_TO_GITHUB_BY_TOKEN_MODAL$TERMS_OF_SERVICE = "CONNECT_TO_GITHUB_BY_TOKEN_MODAL$TERMS_OF_SERVICE",
  SESSION$SERVER_CONNECTED_MESSAGE = "SESSION$SERVER_CONNECTED_MESSAGE",
  SESSION$SESSION_HANDLING_ERROR_MESSAGE = "SESSION$SESSION_HANDLING_ERROR_MESSAGE",
  SESSION$SESSION_CONNECTION_ERROR_MESSAGE = "SESSION$SESSION_CONNECTION_ERROR_MESSAGE",
  SESSION$SOCKET_NOT_INITIALIZED_ERROR_MESSAGE = "SESSION$SOCKET_NOT_INITIALIZED_ERROR_MESSAGE",
  EXPLORER$UPLOAD_ERROR_MESSAGE = "EXPLORER$UPLOAD_ERROR_MESSAGE",
  EXPLORER$LABEL_DROP_FILES = "EXPLORER$LABEL_DROP_FILES",
  EXPLORER$UPLOAD_SUCCESS_MESSAGE = "EXPLORER$UPLOAD_SUCCESS_MESSAGE",
  EXPLORER$NO_FILES_UPLOADED_MESSAGE = "EXPLORER$NO_FILES_UPLOADED_MESSAGE",
  EXPLORER$UPLOAD_PARTIAL_SUCCESS_MESSAGE = "EXPLORER$UPLOAD_PARTIAL_SUCCESS_MESSAGE",
  EXPLORER$UPLOAD_UNEXPECTED_RESPONSE_MESSAGE = "EXPLORER$UPLOAD_UNEXPECTED_RESPONSE_MESSAGE",
  EXPLORER$VSCODE_SWITCHING_MESSAGE = "EXPLORER$VSCODE_SWITCHING_MESSAGE",
  EXPLORER$VSCODE_SWITCHING_ERROR_MESSAGE = "EXPLORER$VSCODE_SWITCHING_ERROR_MESSAGE",
  LOAD_SESSION$MODAL_TITLE = "LOAD_SESSION$MODAL_TITLE",
  LOAD_SESSION$MODAL_CONTENT = "LOAD_SESSION$MODAL_CONTENT",
  LOAD_SESSION$RESUME_SESSION_MODAL_ACTION_LABEL = "LOAD_SESSION$RESUME_SESSION_MODAL_ACTION_LABEL",
  LOAD_SESSION$START_NEW_SESSION_MODAL_ACTION_LABEL = "LOAD_SESSION$START_NEW_SESSION_MODAL_ACTION_LABEL",
  FEEDBACK$MODAL_TITLE = "FEEDBACK$MODAL_TITLE",
  FEEDBACK$MODAL_CONTENT = "FEEDBACK$MODAL_CONTENT",
  FEEDBACK$EMAIL_LABEL = "FEEDBACK$EMAIL_LABEL",
  FEEDBACK$CONTRIBUTE_LABEL = "FEEDBACK$CONTRIBUTE_LABEL",
  FEEDBACK$SHARE_LABEL = "FEEDBACK$SHARE_LABEL",
  FEEDBACK$CANCEL_LABEL = "FEEDBACK$CANCEL_LABEL",
  FEEDBACK$EMAIL_PLACEHOLDER = "FEEDBACK$EMAIL_PLACEHOLDER",
  FEEDBACK$PASSWORD_COPIED_MESSAGE = "FEEDBACK$PASSWORD_COPIED_MESSAGE",
  FEEDBACK$GO_TO_FEEDBACK = "FEEDBACK$GO_TO_FEEDBACK",
  FEEDBACK$PASSWORD = "FEEDBACK$PASSWORD",
  FEEDBACK$INVALID_EMAIL_FORMAT = "FEEDBACK$INVALID_EMAIL_FORMAT",
  FEEDBACK$FAILED_TO_SHARE = "FEEDBACK$FAILED_TO_SHARE",
  FEEDBACK$COPY_LABEL = "FEEDBACK$COPY_LABEL",
  FEEDBACK$SHARING_SETTINGS_LABEL = "FEEDBACK$SHARING_SETTINGS_LABEL",
  SECURITY$UNKNOWN_ANALYZER_LABEL = "SECURITY$UNKNOWN_ANALYZER_LABEL",
  INVARIANT$UPDATE_POLICY_LABEL = "INVARIANT$UPDATE_POLICY_LABEL",
  INVARIANT$UPDATE_SETTINGS_LABEL = "INVARIANT$UPDATE_SETTINGS_LABEL",
  INVARIANT$SETTINGS_LABEL = "INVARIANT$SETTINGS_LABEL",
  INVARIANT$ASK_CONFIRMATION_RISK_SEVERITY_LABEL = "INVARIANT$ASK_CONFIRMATION_RISK_SEVERITY_LABEL",
  INVARIANT$DONT_ASK_FOR_CONFIRMATION_LABEL = "INVARIANT$DONT_ASK_FOR_CONFIRMATION_LABEL",
  INVARIANT$INVARIANT_ANALYZER_LABEL = "INVARIANT$INVARIANT_ANALYZER_LABEL",
  INVARIANT$INVARIANT_ANALYZER_MESSAGE = "INVARIANT$INVARIANT_ANALYZER_MESSAGE",
  INVARIANT$CLICK_TO_LEARN_MORE_LABEL = "INVARIANT$CLICK_TO_LEARN_MORE_LABEL",
  INVARIANT$POLICY_LABEL = "INVARIANT$POLICY_LABEL",
  INVARIANT$LOG_LABEL = "INVARIANT$LOG_LABEL",
  INVARIANT$EXPORT_TRACE_LABEL = "INVARIANT$EXPORT_TRACE_LABEL",
  INVARIANT$TRACE_EXPORTED_MESSAGE = "INVARIANT$TRACE_EXPORTED_MESSAGE",
  INVARIANT$POLICY_UPDATED_MESSAGE = "INVARIANT$POLICY_UPDATED_MESSAGE",
  INVARIANT$SETTINGS_UPDATED_MESSAGE = "INVARIANT$SETTINGS_UPDATED_MESSAGE",
  CHAT_INTERFACE$DISCONNECTED = "CHAT_INTERFACE$DISCONNECTED",
  CHAT_INTERFACE$CONNECTING = "CHAT_INTERFACE$CONNECTING",
  CHAT_INTERFACE$STOPPED = "CHAT_INTERFACE$STOPPED",
  CHAT_INTERFACE$INITIALIZING_AGENT_LOADING_MESSAGE = "CHAT_INTERFACE$INITIALIZING_AGENT_LOADING_MESSAGE",
  CHAT_INTERFACE$AGENT_INIT_MESSAGE = "CHAT_INTERFACE$AGENT_INIT_MESSAGE",
  CHAT_INTERFACE$AGENT_RUNNING_MESSAGE = "CHAT_INTERFACE$AGENT_RUNNING_MESSAGE",
  CHAT_INTERFACE$AGENT_AWAITING_USER_INPUT_MESSAGE = "CHAT_INTERFACE$AGENT_AWAITING_USER_INPUT_MESSAGE",
  CHAT_INTERFACE$AGENT_RATE_LIMITED_MESSAGE = "CHAT_INTERFACE$AGENT_RATE_LIMITED_MESSAGE",
  CHAT_INTERFACE$AGENT_PAUSED_MESSAGE = "CHAT_INTERFACE$AGENT_PAUSED_MESSAGE",
  LANDING$TITLE = "LANDING$TITLE",
  LANDING$SUBTITLE = "LANDING$SUBTITLE",
  LANDING$START_HELP = "LANDING$START_HELP",
  LANDING$START_HELP_LINK = "LANDING$START_HELP_LINK",
  SUGGESTIONS$HELLO_WORLD = "SUGGESTIONS$HELLO_WORLD",
  SUGGESTIONS$TODO_APP = "SUGGESTIONS$TODO_APP",
  SUGGESTIONS$HACKER_NEWS = "SUGGESTIONS$HACKER_NEWS",
  LANDING$CHANGE_PROMPT = "LANDING$CHANGE_PROMPT",
  GITHUB$CONNECT = "GITHUB$CONNECT",
  GITHUB$NO_RESULTS = "GITHUB$NO_RESULTS",
  GITHUB$LOADING_REPOSITORIES = "GITHUB$LOADING_REPOSITORIES",
  GITHUB$ADD_MORE_REPOS = "GITHUB$ADD_MORE_REPOS",
  GITHUB$YOUR_REPOS = "GITHUB$YOUR_REPOS",
  GITHUB$PUBLIC_REPOS = "GITHUB$PUBLIC_REPOS",
  DOWNLOAD$PREPARING = "DOWNLOAD$PREPARING",
  DOWNLOAD$DOWNLOADING = "DOWNLOAD$DOWNLOADING",
  DOWNLOAD$FOUND_FILES = "DOWNLOAD$FOUND_FILES",
  DOWNLOAD$SCANNING = "DOWNLOAD$SCANNING",
  DOWNLOAD$FILES_PROGRESS = "DOWNLOAD$FILES_PROGRESS",
  DOWNLOAD$CANCEL = "DOWNLOAD$CANCEL",
  ACTION$CONFIRM = "ACTION$CONFIRM",
  ACTION$REJECT = "ACTION$REJECT",
  BADGE$BETA = "BADGE$BETA",
  AGENT$RESUME_TASK = "AGENT$RESUME_TASK",
  AGENT$PAUSE_TASK = "AGENT$PAUSE_TASK",
  TOS$ACCEPT = "TOS$ACCEPT",
  TOS$TERMS = "TOS$TERMS",
  USER$ACCOUNT_SETTINGS = "USER$ACCOUNT_SETTINGS",
  JUPYTER$OUTPUT_LABEL = "JUPYTER$OUTPUT_LABEL",
  BUTTON$STOP = "BUTTON$STOP",
  LANDING$ATTACH_IMAGES = "LANDING$ATTACH_IMAGES",
  LANDING$OPEN_REPO = "LANDING$OPEN_REPO",
  LANDING$REPLAY = "LANDING$REPLAY",
  LANDING$UPLOAD_TRAJECTORY = "LANDING$UPLOAD_TRAJECTORY",
  LANDING$RECENT_CONVERSATION = "LANDING$RECENT_CONVERSATION",
  CONVERSATION$CONFIRM_DELETE = "CONVERSATION$CONFIRM_DELETE",
  CONVERSATION$METRICS_INFO = "CONVERSATION$METRICS_INFO",
  CONVERSATION$CREATED = "CONVERSATION$CREATED",
  CONVERSATION$AGO = "CONVERSATION$AGO",
  GITHUB$VSCODE_LINK_DESCRIPTION = "GITHUB$VSCODE_LINK_DESCRIPTION",
  CONVERSATION$EXIT_WARNING = "CONVERSATION$EXIT_WARNING",
  LANDING$OR = "LANDING$OR",
  SUGGESTIONS$TEST_COVERAGE = "SUGGESTIONS$TEST_COVERAGE",
  SUGGESTIONS$AUTO_MERGE = "SUGGESTIONS$AUTO_MERGE",
  CHAT_INTERFACE$AGENT_STOPPED_MESSAGE = "CHAT_INTERFACE$AGENT_STOPPED_MESSAGE",
  CHAT_INTERFACE$AGENT_FINISHED_MESSAGE = "CHAT_INTERFACE$AGENT_FINISHED_MESSAGE",
  CHAT_INTERFACE$AGENT_REJECTED_MESSAGE = "CHAT_INTERFACE$AGENT_REJECTED_MESSAGE",
  CHAT_INTERFACE$AGENT_ERROR_MESSAGE = "CHAT_INTERFACE$AGENT_ERROR_MESSAGE",
  CHAT_INTERFACE$AGENT_AWAITING_USER_CONFIRMATION_MESSAGE = "CHAT_INTERFACE$AGENT_AWAITING_USER_CONFIRMATION_MESSAGE",
  CHAT_INTERFACE$AGENT_ACTION_USER_CONFIRMED_MESSAGE = "CHAT_INTERFACE$AGENT_ACTION_USER_CONFIRMED_MESSAGE",
  CHAT_INTERFACE$AGENT_ACTION_USER_REJECTED_MESSAGE = "CHAT_INTERFACE$AGENT_ACTION_USER_REJECTED_MESSAGE",
  CHAT_INTERFACE$INPUT_PLACEHOLDER = "CHAT_INTERFACE$INPUT_PLACEHOLDER",
  CHAT_INTERFACE$INPUT_CONTINUE_MESSAGE = "CHAT_INTERFACE$INPUT_CONTINUE_MESSAGE",
  CHAT_INTERFACE$USER_ASK_CONFIRMATION = "CHAT_INTERFACE$USER_ASK_CONFIRMATION",
  CHAT_INTERFACE$USER_CONFIRMED = "CHAT_INTERFACE$USER_CONFIRMED",
  CHAT_INTERFACE$USER_REJECTED = "CHAT_INTERFACE$USER_REJECTED",
  CHAT_INTERFACE$INPUT_SEND_MESSAGE_BUTTON_CONTENT = "CHAT_INTERFACE$INPUT_SEND_MESSAGE_BUTTON_CONTENT",
  CHAT_INTERFACE$CHAT_MESSAGE_COPIED = "CHAT_INTERFACE$CHAT_MESSAGE_COPIED",
  CHAT_INTERFACE$CHAT_MESSAGE_COPY_FAILED = "CHAT_INTERFACE$CHAT_MESSAGE_COPY_FAILED",
  CHAT_INTERFACE$TOOLTIP_COPY_MESSAGE = "CHAT_INTERFACE$TOOLTIP_COPY_MESSAGE",
  CHAT_INTERFACE$TOOLTIP_SEND_MESSAGE = "CHAT_INTERFACE$TOOLTIP_SEND_MESSAGE",
  CHAT_INTERFACE$TOOLTIP_UPLOAD_IMAGE = "CHAT_INTERFACE$TOOLTIP_UPLOAD_IMAGE",
  CHAT_INTERFACE$INITIAL_MESSAGE = "CHAT_INTERFACE$INITIAL_MESSAGE",
  CHAT_INTERFACE$ASSISTANT = "CHAT_INTERFACE$ASSISTANT",
  CHAT_INTERFACE$TO_BOTTOM = "CHAT_INTERFACE$TO_BOTTOM",
  CHAT_INTERFACE$MESSAGE_ARIA_LABEL = "CHAT_INTERFACE$MESSAGE_ARIA_LABEL",
  CHAT_INTERFACE$CHAT_CONVERSATION = "CHAT_INTERFACE$CHAT_CONVERSATION",
  CHAT_INTERFACE$UNKNOWN_SENDER = "CHAT_INTERFACE$UNKNOWN_SENDER",
  SECURITY_ANALYZER$UNKNOWN_RISK = "SECURITY_ANALYZER$UNKNOWN_RISK",
  SECURITY_ANALYZER$LOW_RISK = "SECURITY_ANALYZER$LOW_RISK",
  SECURITY_ANALYZER$MEDIUM_RISK = "SECURITY_ANALYZER$MEDIUM_RISK",
  SECURITY_ANALYZER$HIGH_RISK = "SECURITY_ANALYZER$HIGH_RISK",
  SETTINGS$MODEL_TOOLTIP = "SETTINGS$MODEL_TOOLTIP",
  SETTINGS$AGENT_TOOLTIP = "SETTINGS$AGENT_TOOLTIP",
  SETTINGS$LANGUAGE_TOOLTIP = "SETTINGS$LANGUAGE_TOOLTIP",
  SETTINGS$DISABLED_RUNNING = "SETTINGS$DISABLED_RUNNING",
  SETTINGS$API_KEY_PLACEHOLDER = "SETTINGS$API_KEY_PLACEHOLDER",
  SETTINGS$CONFIRMATION_MODE = "SETTINGS$CONFIRMATION_MODE",
  SETTINGS$CONFIRMATION_MODE_TOOLTIP = "SETTINGS$CONFIRMATION_MODE_TOOLTIP",
  SETTINGS$AGENT_SELECT_ENABLED = "SETTINGS$AGENT_SELECT_ENABLED",
  SETTINGS$SECURITY_ANALYZER = "SETTINGS$SECURITY_ANALYZER",
  SETTINGS$SECURITY_ANALYZER_PLACEHOLDER = "SETTINGS$SECURITY_ANALYZER_PLACEHOLDER",
  SETTINGS$DONT_KNOW_API_KEY = "SETTINGS$DONT_KNOW_API_KEY",
  SETTINGS$CLICK_FOR_INSTRUCTIONS = "SETTINGS$CLICK_FOR_INSTRUCTIONS",
  SETTINGS$SAVED = "SETTINGS$SAVED",
  SETTINGS$RESET = "SETTINGS$RESET",
  SETTINGS$API_KEYS = "SETTINGS$API_KEYS",
  SETTINGS$API_KEYS_DESCRIPTION = "SETTINGS$API_KEYS_DESCRIPTION",
  SETTINGS$CREATE_API_KEY = "SETTINGS$CREATE_API_KEY",
  SETTINGS$CREATE_API_KEY_DESCRIPTION = "SETTINGS$CREATE_API_KEY_DESCRIPTION",
  SETTINGS$DELETE_API_KEY = "SETTINGS$DELETE_API_KEY",
  SETTINGS$DELETE_API_KEY_CONFIRMATION = "SETTINGS$DELETE_API_KEY_CONFIRMATION",
  SETTINGS$NO_API_KEYS = "SETTINGS$NO_API_KEYS",
  SETTINGS$NAME = "SETTINGS$NAME",
  SETTINGS$KEY_PREFIX = "SETTINGS$KEY_PREFIX",
  SETTINGS$CREATED_AT = "SETTINGS$CREATED_AT",
  SETTINGS$LAST_USED = "SETTINGS$LAST_USED",
  SETTINGS$ACTIONS = "SETTINGS$ACTIONS",
  SETTINGS$API_KEY_CREATED = "SETTINGS$API_KEY_CREATED",
  SETTINGS$API_KEY_DELETED = "SETTINGS$API_KEY_DELETED",
  SETTINGS$API_KEY_WARNING = "SETTINGS$API_KEY_WARNING",
  SETTINGS$API_KEY_COPIED = "SETTINGS$API_KEY_COPIED",
  SETTINGS$API_KEY_NAME_PLACEHOLDER = "SETTINGS$API_KEY_NAME_PLACEHOLDER",
  BUTTON$CREATE = "BUTTON$CREATE",
  BUTTON$DELETE = "BUTTON$DELETE",
  BUTTON$COPY_TO_CLIPBOARD = "BUTTON$COPY_TO_CLIPBOARD",
  BUTTON$REFRESH = "BUTTON$REFRESH",
  ERROR$REQUIRED_FIELD = "ERROR$REQUIRED_FIELD",
  PLANNER$EMPTY_MESSAGE = "PLANNER$EMPTY_MESSAGE",
  FEEDBACK$PUBLIC_LABEL = "FEEDBACK$PUBLIC_LABEL",
  FEEDBACK$PRIVATE_LABEL = "FEEDBACK$PRIVATE_LABEL",
  SIDEBAR$CONVERSATIONS = "SIDEBAR$CONVERSATIONS",
  STATUS$CONNECTING_TO_RUNTIME = "STATUS$CONNECTING_TO_RUNTIME",
  STATUS$STARTING_RUNTIME = "STATUS$STARTING_RUNTIME",
  STATUS$SETTING_UP_WORKSPACE = "STATUS$SETTING_UP_WORKSPACE",
  STATUS$SETTING_UP_GIT_HOOKS = "STATUS$SETTING_UP_GIT_HOOKS",
  ACCOUNT_SETTINGS_MODAL$DISCONNECT = "ACCOUNT_SETTINGS_MODAL$DISCONNECT",
  ACCOUNT_SETTINGS_MODAL$SAVE = "ACCOUNT_SETTINGS_MODAL$SAVE",
  ACCOUNT_SETTINGS_MODAL$CLOSE = "ACCOUNT_SETTINGS_MODAL$CLOSE",
  ACCOUNT_SETTINGS_MODAL$GITHUB_TOKEN_INVALID = "ACCOUNT_SETTINGS_MODAL$GITHUB_TOKEN_INVALID",
  CONNECT_TO_GITHUB_MODAL$GET_YOUR_TOKEN = "CONNECT_TO_GITHUB_MODAL$GET_YOUR_TOKEN",
  CONNECT_TO_GITHUB_MODAL$HERE = "CONNECT_TO_GITHUB_MODAL$HERE",
  CONNECT_TO_GITHUB_MODAL$CONNECT = "CONNECT_TO_GITHUB_MODAL$CONNECT",
  CONNECT_TO_GITHUB_MODAL$CLOSE = "CONNECT_TO_GITHUB_MODAL$CLOSE",
  CONNECT_TO_GITHUB_BY_TOKEN_MODAL$BY_CONNECTING_YOU_AGREE = "CONNECT_TO_GITHUB_BY_TOKEN_MODAL$BY_CONNECTING_YOU_AGREE",
  CONNECT_TO_GITHUB_BY_TOKEN_MODAL$CONTINUE = "CONNECT_TO_GITHUB_BY_TOKEN_MODAL$CONTINUE",
  LOADING_PROJECT$LOADING = "LOADING_PROJECT$LOADING",
  CUSTOM_INPUT$OPTIONAL_LABEL = "CUSTOM_INPUT$OPTIONAL_LABEL",
  SETTINGS_FORM$CUSTOM_MODEL_LABEL = "SETTINGS_FORM$CUSTOM_MODEL_LABEL",
  SETTINGS_FORM$BASE_URL_LABEL = "SETTINGS_FORM$BASE_URL_LABEL",
  SETTINGS_FORM$API_KEY_LABEL = "SETTINGS_FORM$API_KEY_LABEL",
  SETTINGS_FORM$DONT_KNOW_API_KEY_LABEL = "SETTINGS_FORM$DONT_KNOW_API_KEY_LABEL",
  SETTINGS_FORM$CLICK_HERE_FOR_INSTRUCTIONS_LABEL = "SETTINGS_FORM$CLICK_HERE_FOR_INSTRUCTIONS_LABEL",
  SETTINGS_FORM$AGENT_LABEL = "SETTINGS_FORM$AGENT_LABEL",
  SETTINGS_FORM$SECURITY_ANALYZER_LABEL = "SETTINGS_FORM$SECURITY_ANALYZER_LABEL",
  SETTINGS_FORM$ENABLE_CONFIRMATION_MODE_LABEL = "SETTINGS_FORM$ENABLE_CONFIRMATION_MODE_LABEL",
  SETTINGS_FORM$SAVE_LABEL = "SETTINGS_FORM$SAVE_LABEL",
  SETTINGS_FORM$CLOSE_LABEL = "SETTINGS_FORM$CLOSE_LABEL",
  SETTINGS_FORM$CANCEL_LABEL = "SETTINGS_FORM$CANCEL_LABEL",
  SETTINGS_FORM$END_SESSION_LABEL = "SETTINGS_FORM$END_SESSION_LABEL",
  SETTINGS_FORM$CHANGING_WORKSPACE_WARNING_MESSAGE = "SETTINGS_FORM$CHANGING_WORKSPACE_WARNING_MESSAGE",
  SETTINGS_FORM$ARE_YOU_SURE_LABEL = "SETTINGS_FORM$ARE_YOU_SURE_LABEL",
  SETTINGS_FORM$ALL_INFORMATION_WILL_BE_DELETED_MESSAGE = "SETTINGS_FORM$ALL_INFORMATION_WILL_BE_DELETED_MESSAGE",
  PROJECT_MENU_DETAILS_PLACEHOLDER$NEW_PROJECT_LABEL = "PROJECT_MENU_DETAILS_PLACEHOLDER$NEW_PROJECT_LABEL",
  PROJECT_MENU_DETAILS_PLACEHOLDER$CONNECT_TO_GITHUB = "PROJECT_MENU_DETAILS_PLACEHOLDER$CONNECT_TO_GITHUB",
  PROJECT_MENU_DETAILS_PLACEHOLDER$CONNECTED = "PROJECT_MENU_DETAILS_PLACEHOLDER$CONNECTED",
  PROJECT_MENU_DETAILS$AGO_LABEL = "PROJECT_MENU_DETAILS$AGO_LABEL",
  STATUS$ERROR_LLM_AUTHENTICATION = "STATUS$ERROR_LLM_AUTHENTICATION",
  STATUS$ERROR_LLM_SERVICE_UNAVAILABLE = "STATUS$ERROR_LLM_SERVICE_UNAVAILABLE",
  STATUS$ERROR_LLM_INTERNAL_SERVER_ERROR = "STATUS$ERROR_LLM_INTERNAL_SERVER_ERROR",
  STATUS$ERROR_LLM_OUT_OF_CREDITS = "STATUS$ERROR_LLM_OUT_OF_CREDITS",
  STATUS$ERROR_LLM_CONTENT_POLICY_VIOLATION = "STATUS$ERROR_LLM_CONTENT_POLICY_VIOLATION",
  STATUS$ERROR_RUNTIME_DISCONNECTED = "STATUS$ERROR_RUNTIME_DISCONNECTED",
  STATUS$LLM_RETRY = "STATUS$LLM_RETRY",
  AGENT_ERROR$BAD_ACTION = "AGENT_ERROR$BAD_ACTION",
  AGENT_ERROR$ACTION_TIMEOUT = "AGENT_ERROR$ACTION_TIMEOUT",
  AGENT_ERROR$TOO_MANY_CONVERSATIONS = "AGENT_ERROR$TOO_MANY_CONVERSATIONS",
  PROJECT_MENU_CARD_CONTEXT_MENU$CONNECT_TO_GITHUB_LABEL = "PROJECT_MENU_CARD_CONTEXT_MENU$CONNECT_TO_GITHUB_LABEL",
  PROJECT_MENU_CARD_CONTEXT_MENU$PUSH_TO_GITHUB_LABEL = "PROJECT_MENU_CARD_CONTEXT_MENU$PUSH_TO_GITHUB_LABEL",
  PROJECT_MENU_CARD_CONTEXT_MENU$DOWNLOAD_FILES_LABEL = "PROJECT_MENU_CARD_CONTEXT_MENU$DOWNLOAD_FILES_LABEL",
  PROJECT_MENU_CARD$OPEN = "PROJECT_MENU_CARD$OPEN",
  ACTION_BUTTON$RESUME = "ACTION_BUTTON$RESUME",
  ACTION_BUTTON$PAUSE = "ACTION_BUTTON$PAUSE",
  BROWSER$SCREENSHOT_ALT = "BROWSER$SCREENSHOT_ALT",
  ERROR_TOAST$CLOSE_BUTTON_LABEL = "ERROR_TOAST$CLOSE_BUTTON_LABEL",
  FILE_EXPLORER$UPLOAD = "FILE_EXPLORER$UPLOAD",
  ACTION_MESSAGE$RUN = "ACTION_MESSAGE$RUN",
  ACTION_MESSAGE$RUN_IPYTHON = "ACTION_MESSAGE$RUN_IPYTHON",
  ACTION_MESSAGE$CALL_TOOL_MCP = "ACTION_MESSAGE$CALL_TOOL_MCP",
  ACTION_MESSAGE$READ = "ACTION_MESSAGE$READ",
  ACTION_MESSAGE$EDIT = "ACTION_MESSAGE$EDIT",
  ACTION_MESSAGE$WRITE = "ACTION_MESSAGE$WRITE",
  ACTION_MESSAGE$BROWSE = "ACTION_MESSAGE$BROWSE",
  ACTION_MESSAGE$BROWSE_INTERACTIVE = "ACTION_MESSAGE$BROWSE_INTERACTIVE",
  ACTION_MESSAGE$THINK = "ACTION_MESSAGE$THINK",
  ACTION_MESSAGE$SYSTEM = "ACTION_MESSAGE$SYSTEM",
  ACTION_MESSAGE$CONDENSATION = "ACTION_MESSAGE$CONDENSATION",
  OBSERVATION_MESSAGE$RUN = "OBSERVATION_MESSAGE$RUN",
  OBSERVATION_MESSAGE$RUN_IPYTHON = "OBSERVATION_MESSAGE$RUN_IPYTHON",
  OBSERVATION_MESSAGE$READ = "OBSERVATION_MESSAGE$READ",
  OBSERVATION_MESSAGE$EDIT = "OBSERVATION_MESSAGE$EDIT",
  OBSERVATION_MESSAGE$WRITE = "OBSERVATION_MESSAGE$WRITE",
  OBSERVATION_MESSAGE$BROWSE = "OBSERVATION_MESSAGE$BROWSE",
  OBSERVATION_MESSAGE$MCP = "OBSERVATION_MESSAGE$MCP",
  OBSERVATION_MESSAGE$RECALL = "OBSERVATION_MESSAGE$RECALL",
  OBSERVATION_MESSAGE$THINK = "OBSERVATION_MESSAGE$THINK",
  EXPANDABLE_MESSAGE$SHOW_DETAILS = "EXPANDABLE_MESSAGE$SHOW_DETAILS",
  EXPANDABLE_MESSAGE$HIDE_DETAILS = "EXPANDABLE_MESSAGE$HIDE_DETAILS",
  AI_SETTINGS$TITLE = "AI_SETTINGS$TITLE",
  SETTINGS$DESCRIPTION = "SETTINGS$DESCRIPTION",
  SETTINGS$WARNING = "SETTINGS$WARNING",
  SIDEBAR$SETTINGS = "SIDEBAR$SETTINGS",
  SIDEBAR$DOCS = "SIDEBAR$DOCS",
  SUGGESTIONS$ADD_DOCS = "SUGGESTIONS$ADD_DOCS",
  SUGGESTIONS$ADD_DOCKERFILE = "SUGGESTIONS$ADD_DOCKERFILE",
  STATUS$CONNECTED = "STATUS$CONNECTED",
  BROWSER$NO_PAGE_LOADED = "BROWSER$NO_PAGE_LOADED",
  USER$AVATAR_PLACEHOLDER = "USER$AVATAR_PLACEHOLDER",
  ACCOUNT_SETTINGS$SETTINGS = "ACCOUNT_SETTINGS$SETTINGS",
  ACCOUNT_SETTINGS$LOGOUT = "ACCOUNT_SETTINGS$LOGOUT",
  SETTINGS_FORM$ADVANCED_OPTIONS_LABEL = "SETTINGS_FORM$ADVANCED_OPTIONS_LABEL",
  CONVERSATION$NO_CONVERSATIONS = "CONVERSATION$NO_CONVERSATIONS",
  LANDING$SELECT_GIT_REPO = "LANDING$SELECT_GIT_REPO",
  BUTTON$SEND = "BUTTON$SEND",
  STATUS$BUILDING_RUNTIME = "STATUS$BUILDING_RUNTIME",
  SUGGESTIONS$WHAT_TO_BUILD = "SUGGESTIONS$WHAT_TO_BUILD",
  SETTINGS_FORM$ENABLE_DEFAULT_CONDENSER_SWITCH_LABEL = "SETTINGS_FORM$ENABLE_DEFAULT_CONDENSER_SWITCH_LABEL",
  BUTTON$MARK_HELPFUL = "BUTTON$MARK_HELPFUL",
  BUTTON$MARK_NOT_HELPFUL = "BUTTON$MARK_NOT_HELPFUL",
  BUTTON$EXPORT_CONVERSATION = "BUTTON$EXPORT_CONVERSATION",
  BILLING$CLICK_TO_TOP_UP = "BILLING$CLICK_TO_TOP_UP",
  BILLING$YOUVE_GOT_50 = "BILLING$YOUVE_GOT_50",
  BILLING$ERROR_WHILE_CREATING_SESSION = "BILLING$ERROR_WHILE_CREATING_SESSION",
  BILLING$CLAIM_YOUR_50 = "BILLING$CLAIM_YOUR_50",
  BILLING$POWERED_BY = "BILLING$POWERED_BY",
  BILLING$PROCEED_TO_STRIPE = "BILLING$PROCEED_TO_STRIPE",
  BILLING$YOURE_IN = "BILLING$YOURE_IN",
  PAYMENT$ADD_FUNDS = "PAYMENT$ADD_FUNDS",
  PAYMENT$ADD_CREDIT = "PAYMENT$ADD_CREDIT",
  PAYMENT$MANAGE_CREDITS = "PAYMENT$MANAGE_CREDITS",
  AUTH$SIGN_IN_WITH_GITHUB = "AUTH$SIGN_IN_WITH_GITHUB",
  WAITLIST$JOIN = "WAITLIST$JOIN",
  WAITLIST$IF_NOT_JOINED = "WAITLIST$IF_NOT_JOINED",
  WAITLIST$PATIENCE_MESSAGE = "WAITLIST$PATIENCE_MESSAGE",
  WAITLIST$ALMOST_THERE = "WAITLIST$ALMOST_THERE",
  PAYMENT$SUCCESS = "PAYMENT$SUCCESS",
  PAYMENT$CANCELLED = "PAYMENT$CANCELLED",
  SERVED_APP$TITLE = "SERVED_APP$TITLE",
  CONVERSATION$UNKNOWN = "CONVERSATION$UNKNOWN",
  SETTINGS$RUNTIME_OPTION_1X = "SETTINGS$RUNTIME_OPTION_1X",
  SETTINGS$RUNTIME_OPTION_2X = "SETTINGS$RUNTIME_OPTION_2X",
  SETTINGS$GET_IN_TOUCH = "SETTINGS$GET_IN_TOUCH",
  CONVERSATION$NO_METRICS = "CONVERSATION$NO_METRICS",
  CONVERSATION$DOWNLOAD_ERROR = "CONVERSATION$DOWNLOAD_ERROR",
  CONVERSATION$UPDATED = "CONVERSATION$UPDATED",
  CONVERSATION$TOTAL_COST = "CONVERSATION$TOTAL_COST",
  CONVERSATION$INPUT = "CONVERSATION$INPUT",
  CONVERSATION$OUTPUT = "CONVERSATION$OUTPUT",
  CONVERSATION$TOTAL = "CONVERSATION$TOTAL",
  CONVERSATION$CONTEXT_WINDOW = "CONVERSATION$CONTEXT_WINDOW",
  CONVERSATION$USED = "CONVERSATION$USED",
  SETTINGS$RUNTIME_SETTINGS = "SETTINGS$RUNTIME_SETTINGS",
  SETTINGS$RESET_CONFIRMATION = "SETTINGS$RESET_CONFIRMATION",
  ERROR$GENERIC_OOPS = "ERROR$GENERIC_OOPS",
  ERROR$UNKNOWN = "ERROR$UNKNOWN",
  SETTINGS$FOR_OTHER_OPTIONS = "SETTINGS$FOR_OTHER_OPTIONS",
  SETTINGS$SEE_ADVANCED_SETTINGS = "SETTINGS$SEE_ADVANCED_SETTINGS",
  SETTINGS_FORM$API_KEY = "SETTINGS_FORM$API_KEY",
  SETTINGS_FORM$BASE_URL = "SETTINGS_FORM$BASE_URL",
  GITHUB$CONNECT_TO_GITHUB = "GITHUB$CONNECT_TO_GITHUB",
  GITLAB$CONNECT_TO_GITLAB = "GITLAB$CONNECT_TO_GITLAB",
  AUTH$SIGN_IN_WITH_IDENTITY_PROVIDER = "AUTH$SIGN_IN_WITH_IDENTITY_PROVIDER",
  WAITLIST$JOIN_WAITLIST = "WAITLIST$JOIN_WAITLIST",
  ACCOUNT_SETTINGS$ADDITIONAL_SETTINGS = "ACCOUNT_SETTINGS$ADDITIONAL_SETTINGS",
  ACCOUNT_SETTINGS$DISCONNECT_FROM_GITHUB = "ACCOUNT_SETTINGS$DISCONNECT_FROM_GITHUB",
  CONVERSATION$DELETE_WARNING = "CONVERSATION$DELETE_WARNING",
  FEEDBACK$TITLE = "FEEDBACK$TITLE",
  FEEDBACK$DESCRIPTION = "FEEDBACK$DESCRIPTION",
  EXIT_PROJECT$WARNING = "EXIT_PROJECT$WARNING",
  MODEL_SELECTOR$VERIFIED = "MODEL_SELECTOR$VERIFIED",
  MODEL_SELECTOR$OTHERS = "MODEL_SELECTOR$OTHERS",
  GITLAB$TOKEN_LABEL = "GITLAB$TOKEN_LABEL",
  GITLAB$HOST_LABEL = "GITLAB$HOST_LABEL",
  GITLAB$GET_TOKEN = "GITLAB$GET_TOKEN",
  GITLAB$TOKEN_HELP_TEXT = "GITLAB$TOKEN_HELP_TEXT",
  GITLAB$TOKEN_LINK_TEXT = "GITLAB$TOKEN_LINK_TEXT",
  GITLAB$INSTRUCTIONS_LINK_TEXT = "GITLAB$INSTRUCTIONS_LINK_TEXT",
  GITLAB$OR_SEE = "GITLAB$OR_SEE",
  COMMON$DOCUMENTATION = "COMMON$DOCUMENTATION",
  AGENT_ERROR$ERROR_ACTION_NOT_EXECUTED = "AGENT_ERROR$ERROR_ACTION_NOT_EXECUTED",
  DIFF_VIEWER$LOADING = "DIFF_VIEWER$LOADING",
  DIFF_VIEWER$GETTING_LATEST_CHANGES = "DIFF_VIEWER$GETTING_LATEST_CHANGES",
  DIFF_VIEWER$NOT_A_GIT_REPO = "DIFF_VIEWER$NOT_A_GIT_REPO",
  DIFF_VIEWER$ASK_OH = "DIFF_VIEWER$ASK_OH",
  DIFF_VIEWER$NO_CHANGES = "DIFF_VIEWER$NO_CHANGES",
  DIFF_VIEWER$WAITING_FOR_RUNTIME = "DIFF_VIEWER$WAITING_FOR_RUNTIME",
  SYSTEM_MESSAGE_MODAL$TITLE = "SYSTEM_MESSAGE_MODAL$TITLE",
  SYSTEM_MESSAGE_MODAL$AGENT_CLASS = "SYSTEM_MESSAGE_MODAL$AGENT_CLASS",
  SYSTEM_MESSAGE_MODAL$OPENHANDS_VERSION = "SYSTEM_MESSAGE_MODAL$OPENHANDS_VERSION",
  SYSTEM_MESSAGE_MODAL$SYSTEM_MESSAGE_TAB = "SYSTEM_MESSAGE_MODAL$SYSTEM_MESSAGE_TAB",
  SYSTEM_MESSAGE_MODAL$TOOLS_TAB = "SYSTEM_MESSAGE_MODAL$TOOLS_TAB",
  SYSTEM_MESSAGE_MODAL$PARAMETERS = "SYSTEM_MESSAGE_MODAL$PARAMETERS",
  SYSTEM_MESSAGE_MODAL$NO_TOOLS = "SYSTEM_MESSAGE_MODAL$NO_TOOLS",
  TOS$ACCEPT_TERMS_OF_SERVICE = "TOS$ACCEPT_TERMS_OF_SERVICE",
  TOS$ACCEPT_TERMS_DESCRIPTION = "TOS$ACCEPT_TERMS_DESCRIPTION",
  TOS$CONTINUE = "TOS$CONTINUE",
  TOS$ERROR_ACCEPTING = "TOS$ERROR_ACCEPTING",
  TIPS$CUSTOMIZE_MICROAGENT = "TIPS$CUSTOMIZE_MICROAGENT",
  CONVERSATION$SHOW_MICROAGENTS = "CONVERSATION$SHOW_MICROAGENTS",
  CONVERSATION$NO_MICROAGENTS = "CONVERSATION$NO_MICROAGENTS",
  CONVERSATION$FAILED_TO_FETCH_MICROAGENTS = "CONVERSATION$FAILED_TO_FETCH_MICROAGENTS",
  MICROAGENTS_MODAL$TITLE = "MICROAGENTS_MODAL$TITLE",
  MICROAGENTS_MODAL$TRIGGERS = "MICROAGENTS_MODAL$TRIGGERS",
  MICROAGENTS_MODAL$INPUTS = "MICROAGENTS_MODAL$INPUTS",
  MICROAGENTS_MODAL$TOOLS = "MICROAGENTS_MODAL$TOOLS",
  MICROAGENTS_MODAL$CONTENT = "MICROAGENTS_MODAL$CONTENT",
  MICROAGENTS_MODAL$NO_CONTENT = "MICROAGENTS_MODAL$NO_CONTENT",
  MICROAGENTS_MODAL$FETCH_ERROR = "MICROAGENTS_MODAL$FETCH_ERROR",
  TIPS$SETUP_SCRIPT = "TIPS$SETUP_SCRIPT",
  TIPS$VSCODE_INSTANCE = "TIPS$VSCODE_INSTANCE",
  TIPS$SAVE_WORK = "TIPS$SAVE_WORK",
  TIPS$SPECIFY_FILES = "TIPS$SPECIFY_FILES",
  TIPS$HEADLESS_MODE = "TIPS$HEADLESS_MODE",
  TIPS$CLI_MODE = "TIPS$CLI_MODE",
  TIPS$GITHUB_HOOK = "TIPS$GITHUB_HOOK",
  TIPS$BLOG_SIGNUP = "TIPS$BLOG_SIGNUP",
  TIPS$API_USAGE = "TIPS$API_USAGE",
  TIPS$LEARN_MORE = "TIPS$LEARN_MORE",
  TIPS$PROTIP = "TIPS$PROTIP",
  FEEDBACK$SUBMITTING_LABEL = "FEEDBACK$SUBMITTING_LABEL",
  FEEDBACK$SUBMITTING_MESSAGE = "FEEDBACK$SUBMITTING_MESSAGE",
  SETTINGS$NAV_USER = "SETTINGS$NAV_USER",
  SETTINGS$USER_TITLE = "SETTINGS$USER_TITLE",
  SETTINGS$USER_EMAIL = "SETTINGS$USER_EMAIL",
  SETTINGS$USER_EMAIL_LOADING = "SETTINGS$USER_EMAIL_LOADING",
  SETTINGS$SAVE = "SETTINGS$SAVE",
  SETTINGS$EMAIL_SAVED_SUCCESSFULLY = "SETTINGS$EMAIL_SAVED_SUCCESSFULLY",
  SETTINGS$EMAIL_VERIFIED_SUCCESSFULLY = "SETTINGS$EMAIL_VERIFIED_SUCCESSFULLY",
  SETTINGS$FAILED_TO_SAVE_EMAIL = "SETTINGS$FAILED_TO_SAVE_EMAIL",
  SETTINGS$SENDING = "SETTINGS$SENDING",
  SETTINGS$VERIFICATION_EMAIL_SENT = "SETTINGS$VERIFICATION_EMAIL_SENT",
  SETTINGS$EMAIL_VERIFICATION_REQUIRED = "SETTINGS$EMAIL_VERIFICATION_REQUIRED",
  SETTINGS$INVALID_EMAIL_FORMAT = "SETTINGS$INVALID_EMAIL_FORMAT",
  SETTINGS$EMAIL_VERIFICATION_RESTRICTION_MESSAGE = "SETTINGS$EMAIL_VERIFICATION_RESTRICTION_MESSAGE",
  SETTINGS$RESEND_VERIFICATION = "SETTINGS$RESEND_VERIFICATION",
  SETTINGS$FAILED_TO_RESEND_VERIFICATION = "SETTINGS$FAILED_TO_RESEND_VERIFICATION",
  FEEDBACK$RATE_AGENT_PERFORMANCE = "FEEDBACK$RATE_AGENT_PERFORMANCE",
  FEEDBACK$SELECT_REASON = "FEEDBACK$SELECT_REASON",
  FEEDBACK$SELECT_REASON_COUNTDOWN = "FEEDBACK$SELECT_REASON_COUNTDOWN",
  FEEDBACK$REASON_MISUNDERSTOOD_INSTRUCTION = "FEEDBACK$REASON_MISUNDERSTOOD_INSTRUCTION",
  FEEDBACK$REASON_FORGOT_CONTEXT = "FEEDBACK$REASON_FORGOT_CONTEXT",
  FEEDBACK$REASON_UNNECESSARY_CHANGES = "FEEDBACK$REASON_UNNECESSARY_CHANGES",
  FEEDBACK$REASON_OTHER = "FEEDBACK$REASON_OTHER",
  FEEDBACK$THANK_YOU_FOR_FEEDBACK = "FEEDBACK$THANK_YOU_FOR_FEEDBACK",
  FEEDBACK$FAILED_TO_SUBMIT = "FEEDBACK$FAILED_TO_SUBMIT",
}
